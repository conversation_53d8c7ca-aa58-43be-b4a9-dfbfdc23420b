"use strict";const e=require("../../../common/vendor.js");if(!Array){(e.resolveComponent("mall-navbar")+e.resolveComponent("mall-button"))()}Math||((()=>"../../../components/mall-navbar/mall-navbar.js")+(()=>"../../../components/mall-button/mall-button.js"))();const t=e.defineComponent({__name:"index",setup(t){const a=e.ref(""),n=e.ref({id:"",orderNo:"",userId:"",status:"pending",items:[],totalAmount:0,discountAmount:0,shippingFee:0,payAmount:0,payMethod:"wechat",shippingAddress:{id:"",userId:"",name:"",phone:"",province:"",city:"",district:"",detail:"",isDefault:!1,createdAt:"",updatedAt:""},createdAt:"",updatedAt:""}),i=e.ref({description:"商品已从深圳发出，正在运输途中"}),s=e.computed(()=>{const e=[];switch(n.value.status){case"pending":e.push({type:"cancel",text:"取消订单",buttonType:"default"},{type:"pay",text:"立即付款",buttonType:"primary"});break;case"paid":e.push({type:"remind",text:"提醒发货",buttonType:"default"});break;case"shipped":e.push({type:"logistics",text:"查看物流",buttonType:"default"},{type:"confirm",text:"确认收货",buttonType:"primary"});break;case"delivered":e.push({type:"review",text:"评价",buttonType:"primary"});break;case"completed":e.push({type:"buy-again",text:"再次购买",buttonType:"default"})}return e});e.onLoad(e=>{(null==e?void 0:e.id)&&(a.value=e.id,o())});const o=()=>{return t=this,i=null,s=function*(){try{n.value={id:a.value,orderNo:`ORDER${Date.now()}`,userId:"1",status:"shipped",items:[{id:"1",productId:"product_1",productName:"iPhone 15 Pro Max 256GB",productImage:"https://picsum.photos/120/120?random=1",quantity:1,price:9999,totalPrice:9999,selectedSpecs:[{specId:"color",specName:"颜色",optionId:"black",optionName:"深空黑",optionValue:"black"},{specId:"storage",specName:"存储",optionId:"256gb",optionName:"256GB",optionValue:"256gb"}]}],totalAmount:9999,discountAmount:100,shippingFee:10,payAmount:9909,payMethod:"wechat",payTime:new Date(Date.now()-1728e5).toISOString(),shippingAddress:{id:"1",userId:"1",name:"张三",phone:"13800138000",province:"广东省",city:"深圳市",district:"南山区",detail:"科技园南区深南大道xxx号",isDefault:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},remark:"请尽快发货",createdAt:new Date(Date.now()-2592e5).toISOString(),updatedAt:(new Date).toISOString()}}catch(t){e.index.showToast({title:"加载失败",icon:"none"})}},new Promise((e,a)=>{var n=e=>{try{d(s.next(e))}catch(t){a(t)}},o=e=>{try{d(s.throw(e))}catch(t){a(t)}},d=t=>t.done?e(t.value):Promise.resolve(t.value).then(n,o);d((s=s.apply(t,i)).next())});var t,i,s},d=e=>({pending:"待付款",paid:"待发货",shipped:"运输中",delivered:"待评价",completed:"已完成",cancelled:"已取消",refunding:"退款中",refunded:"已退款"}[e]||"未知状态"),u=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),p=()=>{e.index.navigateTo({url:`/pages/order/logistics/index?orderId=${a.value}`})};return(t,o)=>{return e.e({a:e.p({title:"订单详情","show-back":!0}),b:e.n((r=n.value.status,{pending:"icon-clock",paid:"icon-check",shipped:"icon-truck",delivered:"icon-home",completed:"icon-success",cancelled:"icon-close",refunding:"icon-refresh",refunded:"icon-success"}[r]||"icon-info")),c:e.t(d(n.value.status)),d:"shipped"===n.value.status},"shipped"===n.value.status?{}:"pending"===n.value.status?{f:e.t("23分58秒")}:{},{e:"pending"===n.value.status,g:"shipped"===n.value.status||"delivered"===n.value.status},"shipped"===n.value.status||"delivered"===n.value.status?{h:e.t(i.value.description),i:e.o(p)}:{},{j:e.t(n.value.shippingAddress.name),k:e.t(n.value.shippingAddress.phone),l:e.t(n.value.shippingAddress.province),m:e.t(n.value.shippingAddress.city),n:e.t(n.value.shippingAddress.district),o:e.t(n.value.shippingAddress.detail),p:e.f(n.value.items,(t,a,n)=>e.e({a:t.productImage,b:e.t(t.productName),c:t.selectedSpecs.length>0},t.selectedSpecs.length>0?{d:e.f(t.selectedSpecs,(t,a,n)=>({a:e.t(t.specName),b:e.t(t.optionName),c:t.specId}))}:{},{e:e.t(t.price),f:e.t(t.quantity),g:t.id})),q:e.t(n.value.orderNo),r:e.t(u(n.value.createdAt)),s:n.value.payTime},n.value.payTime?{t:e.t(u(n.value.payTime))}:{},{v:e.t((c=n.value.payMethod,{wechat:"微信支付",alipay:"支付宝",balance:"余额支付"}[c]||"未知支付方式")),w:n.value.remark},n.value.remark?{x:e.t(n.value.remark)}:{},{y:e.t(n.value.totalAmount),z:e.t(n.value.shippingFee>0?`¥${n.value.shippingFee}`:"免费"),A:n.value.discountAmount>0},n.value.discountAmount>0?{B:e.t(n.value.discountAmount)}:{},{C:e.t(n.value.payAmount),D:s.value.length>0},s.value.length>0?{E:e.f(s.value,(t,i,s)=>({a:e.t(t.text),b:t.type,c:e.o(i=>(t=>{switch(t){case"pay":e.index.navigateTo({url:`/pages/order/pay/index?orderId=${a.value}&amount=${n.value.payAmount}`});break;case"cancel":e.index.showModal({title:"确认取消",content:"确定要取消这个订单吗？",success:t=>{t.confirm&&(e.index.showToast({title:"订单已取消",icon:"success"}),n.value.status="cancelled")}});break;case"confirm":e.index.showModal({title:"确认收货",content:"确定已收到商品吗？",success:t=>{t.confirm&&(e.index.showToast({title:"确认收货成功",icon:"success"}),n.value.status="delivered")}});break;case"logistics":p();break;case"review":e.index.navigateTo({url:`/pages/order/review/index?orderId=${a.value}`});break;case"buy-again":e.index.showToast({title:"已加入购物车",icon:"success"});break;default:e.index.showToast({title:"功能开发中",icon:"none"})}})(t.type),t.type),d:"3ea297b8-1-"+s,e:e.p({type:t.buttonType})}))}:{});var c,r}}}),a=e._export_sfc(t,[["__scopeId","data-v-3ea297b8"]]);wx.createPage(a);
