"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),o=require("../../store/app.js"),a=e.defineComponent({__name:"mall-navbar",props:{title:{default:""},showBack:{type:Boolean,default:!0},backgroundColor:{default:"#ffffff"},textColor:{default:"#333333"},fixed:{type:Boolean,default:!0},safeAreaInsetTop:{type:Boolean,default:!0}},emits:["back"],setup(a,{emit:r}){const n=a,s=r,i=o.useAppStore(),d=e.computed(()=>{const e=i.getStatusBarHeight(),t=i.getNavBarHeight();return{backgroundColor:n.backgroundColor,color:n.textColor,position:n.fixed?"fixed":"relative",top:n.fixed?"0":"auto",paddingTop:n.safeAreaInsetTop?`${e}px`:"0",height:`${e+t}px`,zIndex:n.fixed?1e3:"auto"}}),p=()=>{s("back"),getCurrentPages().length>1?e.index.navigateBack():e.index.switchTab({url:"/pages/home/<USER>"})};return(o,a)=>e.e({a:o.showBack},o.showBack?{b:t._imports_0$5,c:e.o(p)}:{},{d:e.t(o.title),e:e.s(d.value)})}}),r=e._export_sfc(a,[["__scopeId","data-v-eb91c212"]]);wx.createComponent(r);
