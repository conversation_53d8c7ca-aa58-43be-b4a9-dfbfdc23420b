@use "variables" as *;

// 重置样式
* {
  box-sizing: border-box;
}

page {
  background-color: $bg-color-page;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-color-primary;
}

// 通用布局类
.container {
  padding: 0 $spacing-lg;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

// 文字对齐
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

// 文字颜色
.text-primary {
  color: $primary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-secondary {
  color: $text-color-secondary;
}

.text-placeholder {
  color: $text-color-placeholder;
}

.text-white {
  color: $text-color-white;
}

// 字体大小
.text-xs {
  font-size: $font-size-xs;
}

.text-sm {
  font-size: $font-size-sm;
}

.text-base {
  font-size: $font-size-base;
}

.text-lg {
  font-size: $font-size-lg;
}

.text-xl {
  font-size: $font-size-xl;
}

.text-xxl {
  font-size: $font-size-xxl;
}

.text-title {
  font-size: $font-size-title;
}

// 字体粗细
.font-normal {
  font-weight: $font-weight-normal;
}

.font-medium {
  font-weight: $font-weight-medium;
}

.font-bold {
  font-weight: $font-weight-bold;
}

// 背景色
.bg-white {
  background-color: $bg-color-white;
}

.bg-gray {
  background-color: $bg-color-gray;
}

.bg-primary {
  background-color: $primary-color;
}

// 边距
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-xs { margin: $spacing-xs; }
.mt-xs { margin-top: $spacing-xs; }
.mr-xs { margin-right: $spacing-xs; }
.mb-xs { margin-bottom: $spacing-xs; }
.ml-xs { margin-left: $spacing-xs; }

.m-sm { margin: $spacing-sm; }
.mt-sm { margin-top: $spacing-sm; }
.mr-sm { margin-right: $spacing-sm; }
.mb-sm { margin-bottom: $spacing-sm; }
.ml-sm { margin-left: $spacing-sm; }

.m-md { margin: $spacing-md; }
.mt-md { margin-top: $spacing-md; }
.mr-md { margin-right: $spacing-md; }
.mb-md { margin-bottom: $spacing-md; }
.ml-md { margin-left: $spacing-md; }

.m-lg { margin: $spacing-lg; }
.mt-lg { margin-top: $spacing-lg; }
.mr-lg { margin-right: $spacing-lg; }
.mb-lg { margin-bottom: $spacing-lg; }
.ml-lg { margin-left: $spacing-lg; }

// 内边距
.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-xs { padding: $spacing-xs; }
.pt-xs { padding-top: $spacing-xs; }
.pr-xs { padding-right: $spacing-xs; }
.pb-xs { padding-bottom: $spacing-xs; }
.pl-xs { padding-left: $spacing-xs; }

.p-sm { padding: $spacing-sm; }
.pt-sm { padding-top: $spacing-sm; }
.pr-sm { padding-right: $spacing-sm; }
.pb-sm { padding-bottom: $spacing-sm; }
.pl-sm { padding-left: $spacing-sm; }

.p-md { padding: $spacing-md; }
.pt-md { padding-top: $spacing-md; }
.pr-md { padding-right: $spacing-md; }
.pb-md { padding-bottom: $spacing-md; }
.pl-md { padding-left: $spacing-md; }

.p-lg { padding: $spacing-lg; }
.pt-lg { padding-top: $spacing-lg; }
.pr-lg { padding-right: $spacing-lg; }
.pb-lg { padding-bottom: $spacing-lg; }
.pl-lg { padding-left: $spacing-lg; }

// 圆角
.rounded-sm {
  border-radius: $border-radius-small;
}

.rounded {
  border-radius: $border-radius-base;
}

.rounded-lg {
  border-radius: $border-radius-large;
}

.rounded-full {
  border-radius: $border-radius-round;
}

// 边框
.border {
  border: 1px solid $border-color-base;
}

.border-light {
  border: 1px solid $border-color-light;
}

.border-dark {
  border: 1px solid $border-color-dark;
}

// 阴影
.shadow-sm {
  box-shadow: $box-shadow-light;
}

.shadow {
  box-shadow: $box-shadow-base;
}

.shadow-lg {
  box-shadow: $box-shadow-dark;
}

// 隐藏/显示
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

// 省略号
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
