<view class="profile-page data-v-7aedad92"><mall-navbar wx:if="{{a}}" class="data-v-7aedad92" u-i="7aedad92-0" bind:__l="__l" u-p="{{a}}"/><view class="avatar-section data-v-7aedad92"><view class="avatar-item data-v-7aedad92" bindtap="{{c}}"><text class="item-label data-v-7aedad92">头像</text><view class="avatar-value data-v-7aedad92"><image src="{{b}}" class="user-avatar data-v-7aedad92"/><text class="iconfont icon-arrow-right data-v-7aedad92"></text></view></view></view><view class="info-section data-v-7aedad92"><view class="info-item data-v-7aedad92" bindtap="{{e}}"><text class="item-label data-v-7aedad92">昵称</text><view class="item-value data-v-7aedad92"><text class="value-text data-v-7aedad92">{{d}}</text><text class="iconfont icon-arrow-right data-v-7aedad92"></text></view></view><view class="info-item data-v-7aedad92" bindtap="{{g}}"><text class="item-label data-v-7aedad92">性别</text><view class="item-value data-v-7aedad92"><text class="value-text data-v-7aedad92">{{f}}</text><text class="iconfont icon-arrow-right data-v-7aedad92"></text></view></view><view class="info-item data-v-7aedad92" bindtap="{{i}}"><text class="item-label data-v-7aedad92">生日</text><view class="item-value data-v-7aedad92"><text class="value-text data-v-7aedad92">{{h}}</text><text class="iconfont icon-arrow-right data-v-7aedad92"></text></view></view><view class="info-item data-v-7aedad92" bindtap="{{k}}"><text class="item-label data-v-7aedad92">手机号</text><view class="item-value data-v-7aedad92"><text class="value-text data-v-7aedad92">{{j}}</text><text class="iconfont icon-arrow-right data-v-7aedad92"></text></view></view></view><view class="save-section data-v-7aedad92"><mall-button wx:if="{{m}}" class="data-v-7aedad92" u-s="{{['d']}}" bindclick="{{l}}" u-i="7aedad92-1" bind:__l="__l" u-p="{{m}}"> 保存修改 </mall-button></view><uni-popup wx:if="{{v}}" class="r data-v-7aedad92" u-s="{{['d']}}" u-r="nicknamePopup" u-i="7aedad92-2" bind:__l="__l" u-p="{{v}}"><view class="edit-popup data-v-7aedad92"><view class="popup-header data-v-7aedad92"><text class="popup-title data-v-7aedad92">修改昵称</text></view><view class="popup-content data-v-7aedad92"><input class="edit-input data-v-7aedad92" placeholder="请输入昵称" maxlength="20" value="{{n}}" bindinput="{{o}}"/></view><view class="popup-actions data-v-7aedad92"><mall-button wx:if="{{q}}" class="data-v-7aedad92" u-s="{{['d']}}" bindclick="{{p}}" u-i="7aedad92-3,7aedad92-2" bind:__l="__l" u-p="{{q}}">取消</mall-button><mall-button wx:if="{{s}}" class="data-v-7aedad92" u-s="{{['d']}}" bindclick="{{r}}" u-i="7aedad92-4,7aedad92-2" bind:__l="__l" u-p="{{s}}">确定</mall-button></view></view></uni-popup><uni-popup wx:if="{{z}}" class="r data-v-7aedad92" u-s="{{['d']}}" u-r="genderPopup" u-i="7aedad92-5" bind:__l="__l" u-p="{{z}}"><view class="select-popup data-v-7aedad92"><view class="popup-header data-v-7aedad92"><text class="popup-title data-v-7aedad92">选择性别</text><text class="popup-close data-v-7aedad92" bindtap="{{w}}">×</text></view><view class="select-options data-v-7aedad92"><view wx:for="{{x}}" wx:for-item="option" wx:key="c" class="{{['select-option', 'data-v-7aedad92', option.d && 'active']}}" bindtap="{{option.e}}"><text class="data-v-7aedad92">{{option.a}}</text><text wx:if="{{option.b}}" class="iconfont icon-check data-v-7aedad92"></text></view></view></view></uni-popup><picker wx:if="{{A}}" class="data-v-7aedad92" mode="date" value="{{B}}" bindchange="{{C}}" bindcancel="{{D}}"><view class="data-v-7aedad92"></view></picker></view>