<template>
  <view class="login-page">
    <!-- 顶部装饰 -->
    <view class="login-header">
      <view class="header-bg"></view>
      <view class="logo-section">
        <image src="/static/images/logo.png" class="app-logo" mode="aspectFit" />
        <text class="app-name">商城小程序</text>
      </view>
    </view>
    
    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-title">
        <text class="title-text">欢迎登录</text>
        <text class="subtitle-text">请使用微信授权登录</text>
      </view>
      
      <!-- 微信授权登录 -->
      <view class="wechat-login">
        <button
          class="wechat-login-btn"
          open-type="getUserInfo"
          @getuserinfo="handleWechatLogin"
          :loading="loginLoading"
        >
          <text class="iconfont icon-wechat"></text>
          <text class="btn-text">微信授权登录</text>
        </button>
        
        <view class="login-tips">
          <text class="tips-text">登录即表示同意</text>
          <text class="link-text" @click="showAgreement('privacy')">《隐私政策》</text>
          <text class="tips-text">和</text>
          <text class="link-text" @click="showAgreement('terms')">《用户协议》</text>
        </view>
      </view>
      
      <!-- 手机号登录 -->
      <view class="phone-login">
        <view class="form-item">
          <text class="item-label">手机号</text>
          <input
            v-model="phoneForm.phone"
            type="number"
            placeholder="请输入手机号"
            class="form-input"
            maxlength="11"
          />
        </view>
        
        <view class="form-item">
          <text class="item-label">验证码</text>
          <view class="code-input-group">
            <input
              v-model="phoneForm.code"
              type="number"
              placeholder="请输入验证码"
              class="form-input"
              maxlength="6"
            />
            <mall-button
              type="default"
              size="small"
              :disabled="!canSendCode || codeCountdown > 0"
              :loading="sendingCode"
              @click="sendVerifyCode"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}s` : '发送验证码' }}
            </mall-button>
          </view>
        </view>
        
        <mall-button
          type="primary"
          :disabled="!canPhoneLogin"
          :loading="phoneLoginLoading"
          @click="handlePhoneLogin"
        >
          手机号登录
        </mall-button>
      </view>
      
      <!-- 切换登录方式 -->
      <view class="login-switch">
        <text class="switch-text" @click="switchLoginType">
          {{ loginType === 'wechat' ? '使用手机号登录' : '使用微信登录' }}
        </text>
      </view>
    </view>
    
    <!-- 游客模式 -->
    <view class="guest-mode">
      <text class="guest-text" @click="guestLogin">暂不登录，先逛逛</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 登录类型
const loginType = ref<'wechat' | 'phone'>('wechat')

// 加载状态
const loginLoading = ref(false)
const phoneLoginLoading = ref(false)
const sendingCode = ref(false)

// 验证码倒计时
const codeCountdown = ref(0)
let countdownTimer: NodeJS.Timeout | null = null

// 手机号登录表单
const phoneForm = ref({
  phone: '',
  code: ''
})

// 计算属性
const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(phoneForm.value.phone)
})

const canPhoneLogin = computed(() => {
  return canSendCode.value && phoneForm.value.code.length === 6
})

// 微信授权登录
const handleWechatLogin = async (e: any) => {
  const { userInfo } = e.detail
  
  if (!userInfo) {
    uni.showToast({
      title: '授权失败，请重试',
      icon: 'none'
    })
    return
  }
  
  loginLoading.value = true
  
  try {
    // 获取微信登录凭证
    const loginRes = await uni.login({
      provider: 'weixin'
    })
    
    if (!loginRes[1].code) {
      throw new Error('获取登录凭证失败')
    }
    
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存用户信息
    const userData = {
      id: 'user_' + Date.now(),
      nickname: userInfo.nickName,
      avatar: userInfo.avatarUrl,
      phone: '',
      gender: userInfo.gender,
      openid: 'mock_openid',
      token: 'mock_token_' + Date.now()
    }
    
    // 存储到本地
    uni.setStorageSync('userInfo', userData)
    uni.setStorageSync('token', userData.token)
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
    
    // 返回上一页或跳转到首页
    setTimeout(() => {
      const pages = getCurrentPages()
      if (pages.length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    }, 1500)
    
  } catch (error) {
    console.error('微信登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    })
  } finally {
    loginLoading.value = false
  }
}

// 发送验证码
const sendVerifyCode = async () => {
  if (!canSendCode.value || sendingCode.value) return
  
  sendingCode.value = true
  
  try {
    // 模拟发送验证码API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
    
    // 开始倒计时
    startCountdown()
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none'
    })
  } finally {
    sendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  codeCountdown.value = 60
  countdownTimer = setInterval(() => {
    codeCountdown.value--
    if (codeCountdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)
}

// 手机号登录
const handlePhoneLogin = async () => {
  if (!canPhoneLogin.value) return
  
  phoneLoginLoading.value = true
  
  try {
    // 模拟手机号登录API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存用户信息
    const userData = {
      id: 'user_' + Date.now(),
      nickname: '用户' + phoneForm.value.phone.slice(-4),
      avatar: '/static/images/default-avatar.png',
      phone: phoneForm.value.phone,
      gender: 0,
      token: 'mock_token_' + Date.now()
    }
    
    // 存储到本地
    uni.setStorageSync('userInfo', userData)
    uni.setStorageSync('token', userData.token)
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
    
    // 返回上一页或跳转到首页
    setTimeout(() => {
      const pages = getCurrentPages()
      if (pages.length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    }, 1500)
    
  } catch (error) {
    console.error('手机号登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    })
  } finally {
    phoneLoginLoading.value = false
  }
}

// 切换登录方式
const switchLoginType = () => {
  loginType.value = loginType.value === 'wechat' ? 'phone' : 'wechat'
}

// 游客登录
const guestLogin = () => {
  // 设置游客标识
  uni.setStorageSync('isGuest', true)
  
  // 返回上一页或跳转到首页
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    uni.switchTab({
      url: '/pages/home/<USER>'
    })
  }
}

// 显示协议
const showAgreement = (type: 'privacy' | 'terms') => {
  const title = type === 'privacy' ? '隐私政策' : '用户协议'
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${title}&url=https://example.com/${type}`
  })
}

// 页面销毁时清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style lang="scss" scoped>
@use "sass:color";

.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-color 0%, color.adjust($primary-color, $lightness: 10%) 100%);
}

.login-header {
  position: relative;
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/static/images/login-bg.png') center/cover;
    opacity: 0.1;
  }
  
  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    
    .app-logo {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: $spacing-md;
    }

    .app-name {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $bg-color-white;
    }
  }
}

.login-form {
  background-color: $bg-color-white;
  border-radius: 32rpx 32rpx 0 0;
  padding: $spacing-lg;
  margin-top: -32rpx;
  position: relative;
  z-index: 2;
  
  .form-title {
    text-align: center;
    margin-bottom: $spacing-lg * 2;

    .title-text {
      font-size: 48rpx;
      font-weight: 600;
      color: $text-color-primary;
      display: block;
      margin-bottom: $spacing-sm;
    }

    .subtitle-text {
      font-size: $font-size-base;
      color: $text-color-regular;
    }
  }
}

.wechat-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .wechat-login-btn {
    width: 100%;
    height: 88rpx;
    background-color: #07c160;
    color: $bg-color-white;
    border: none;
    border-radius: $border-radius-base;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-size-base;
    margin-bottom: $spacing-md;

    .iconfont {
      font-size: 40rpx;
      margin-right: $spacing-sm;
    }
    
    &::after {
      border: none;
    }
    
    &.button-hover {
      background-color: color.adjust(#07c160, $lightness: -5%);
    }
  }
  
  .login-tips {
    text-align: center;
    
    .tips-text {
      font-size: $font-size-small;
      color: $text-color-placeholder;
    }
    
    .link-text {
      font-size: $font-size-small;
      color: $primary-color;
    }
  }
}

.phone-login {
  .form-item {
    margin-bottom: $spacing-lg;

    .item-label {
      font-size: $font-size-base;
      color: $text-color-primary;
      display: block;
      margin-bottom: $spacing-sm;
    }

    .form-input {
      width: 100%;
      height: 88rpx;
      padding: 0 $spacing-md;
      border: 1px solid $border-color-base;
      border-radius: $border-radius-base;
      font-size: $font-size-base;
      background-color: $bg-color-white;
    }

    .code-input-group {
      display: flex;
      gap: $spacing-md;
      
      .form-input {
        flex: 1;
      }
    }
  }
}

.login-switch {
  text-align: center;
  margin-top: $spacing-lg;

  .switch-text {
    font-size: $font-size-base;
    color: $primary-color;
  }
}

.guest-mode {
  text-align: center;
  padding: $spacing-lg;
  
  .guest-text {
    font-size: $font-size-base;
    color: rgba($bg-color-white, 0.8);
  }
}

// 根据登录类型显示不同内容
.login-form[data-type="wechat"] .phone-login {
  display: none;
}

.login-form[data-type="phone"] .wechat-login {
  display: none;
}
</style>
