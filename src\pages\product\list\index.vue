<template>
  <view class="product-list-page">
    <!-- 导航栏 -->
    <mall-navbar title="商品列表" :show-back="true" />
    
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="showSortPopup = true">
        <text class="filter-text">{{ currentSort.label }}</text>
        <text class="iconfont icon-arrow-down"></text>
      </view>
      <view class="filter-item" @click="showFilterPopup = true">
        <text class="filter-text">筛选</text>
        <text class="iconfont icon-filter"></text>
      </view>
    </view>
    
    <!-- 商品列表 -->
    <view class="product-list">
      <view class="product-grid">
        <mall-product-card
          v-for="product in productList"
          :key="product.id"
          :product="product"
          @click="goToDetail(product.id)"
        />
      </view>
      
      <!-- 加载更多 -->
      <view v-if="loading" class="loading-more">
        <text>加载中...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view v-else-if="!hasMore && productList.length > 0" class="no-more">
        <text>没有更多商品了</text>
      </view>
      
      <!-- 空状态 -->
      <view v-else-if="productList.length === 0 && !loading" class="empty-state">
        <text class="empty-text">暂无商品</text>
      </view>
    </view>
    
    <!-- 排序弹窗 -->
    <uni-popup ref="sortPopup" type="bottom">
      <view class="sort-popup">
        <view class="popup-header">
          <text class="popup-title">排序方式</text>
          <text class="popup-close" @click="showSortPopup = false">×</text>
        </view>
        <view class="sort-options">
          <view
            v-for="option in sortOptions"
            :key="option.value"
            class="sort-option"
            :class="{ active: currentSort.value === option.value }"
            @click="selectSort(option)"
          >
            <text>{{ option.label }}</text>
            <text v-if="currentSort.value === option.value" class="iconfont icon-check"></text>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="right">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">筛选</text>
          <text class="popup-close" @click="showFilterPopup = false">×</text>
        </view>
        
        <!-- 价格筛选 -->
        <view class="filter-section">
          <view class="section-title">价格区间</view>
          <view class="price-range">
            <input
              v-model="filterData.minPrice"
              type="number"
              placeholder="最低价"
              class="price-input"
            />
            <text class="price-separator">-</text>
            <input
              v-model="filterData.maxPrice"
              type="number"
              placeholder="最高价"
              class="price-input"
            />
          </view>
        </view>
        
        <!-- 分类筛选 -->
        <view class="filter-section">
          <view class="section-title">商品分类</view>
          <view class="category-list">
            <view
              v-for="category in categories"
              :key="category.id"
              class="category-item"
              :class="{ active: filterData.categoryId === category.id }"
              @click="selectCategory(category.id)"
            >
              <text>{{ category.name }}</text>
            </view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="filter-actions">
          <mall-button type="default" @click="resetFilter">重置</mall-button>
          <mall-button type="primary" @click="applyFilter">确定</mall-button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import type { Product, Category, ProductQuery } from '@/types'

// 页面参数
const props = defineProps<{
  categoryId?: string
  keyword?: string
}>()

// 响应式数据
const productList = ref<Product[]>([])
const categories = ref<Category[]>([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 20

// 弹窗状态
const showSortPopup = ref(false)
const showFilterPopup = ref(false)

// 排序选项
const sortOptions = [
  { label: '综合排序', value: 'default' },
  { label: '销量优先', value: 'sales' },
  { label: '价格从低到高', value: 'price_asc' },
  { label: '价格从高到低', value: 'price_desc' },
  { label: '最新上架', value: 'newest' }
]

const currentSort = ref(sortOptions[0])

// 筛选数据
const filterData = ref({
  categoryId: props.categoryId || '',
  minPrice: '',
  maxPrice: '',
  keyword: props.keyword || ''
})

// 页面生命周期
onMounted(() => {
  loadCategories()
  loadProductList(true)
})

// 触底加载更多
onReachBottom(() => {
  if (!loading.value && hasMore.value) {
    loadProductList(false)
  }
})

// 加载商品列表
const loadProductList = async (refresh = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    if (refresh) {
      currentPage.value = 1
      productList.value = []
    }
    
    const query: ProductQuery = {
      page: currentPage.value,
      pageSize,
      categoryId: filterData.value.categoryId,
      keyword: filterData.value.keyword,
      minPrice: filterData.value.minPrice ? Number(filterData.value.minPrice) : undefined,
      maxPrice: filterData.value.maxPrice ? Number(filterData.value.maxPrice) : undefined,
      sortBy: getSortField(),
      sortOrder: getSortOrder()
    }
    
    // 模拟API调用
    const mockProducts = generateMockProducts(currentPage.value, pageSize)
    
    if (refresh) {
      productList.value = mockProducts
    } else {
      productList.value.push(...mockProducts)
    }
    
    hasMore.value = mockProducts.length === pageSize
    currentPage.value++
  } catch (error) {
    console.error('加载商品列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载分类列表
const loadCategories = async () => {
  try {
    // 模拟分类数据
    categories.value = [
      { id: '1', name: '手机数码', icon: '', parentId: '', sort: 1, status: 'active' },
      { id: '2', name: '服装鞋包', icon: '', parentId: '', sort: 2, status: 'active' },
      { id: '3', name: '家居生活', icon: '', parentId: '', sort: 3, status: 'active' },
      { id: '4', name: '美妆护肤', icon: '', parentId: '', sort: 4, status: 'active' }
    ]
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 生成模拟商品数据
const generateMockProducts = (page: number, size: number): Product[] => {
  const products: Product[] = []
  const start = (page - 1) * size
  
  for (let i = 0; i < size; i++) {
    const id = start + i + 1
    products.push({
      id: id.toString(),
      name: `商品名称 ${id}`,
      description: `这是商品 ${id} 的详细描述`,
      price: Math.floor(Math.random() * 1000) + 100,
      originalPrice: Math.floor(Math.random() * 1500) + 200,
      images: [`https://picsum.photos/300/300?random=${id}`],
      categoryId: '1',
      categoryName: '手机数码',
      stock: Math.floor(Math.random() * 100) + 10,
      sales: Math.floor(Math.random() * 1000),
      rating: 4 + Math.random(),
      reviewCount: Math.floor(Math.random() * 500),
      tags: ['热销', '推荐'],
      specs: [],
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
  }
  
  return products
}

// 获取排序字段
const getSortField = () => {
  switch (currentSort.value.value) {
    case 'sales': return 'sales'
    case 'price_asc':
    case 'price_desc': return 'price'
    case 'newest': return 'createdAt'
    default: return 'sort'
  }
}

// 获取排序方向
const getSortOrder = () => {
  return currentSort.value.value === 'price_desc' ? 'desc' : 'asc'
}

// 选择排序方式
const selectSort = (option: typeof sortOptions[0]) => {
  currentSort.value = option
  showSortPopup.value = false
  loadProductList(true)
}

// 选择分类
const selectCategory = (categoryId: string) => {
  filterData.value.categoryId = filterData.value.categoryId === categoryId ? '' : categoryId
}

// 重置筛选
const resetFilter = () => {
  filterData.value = {
    categoryId: '',
    minPrice: '',
    maxPrice: '',
    keyword: ''
  }
}

// 应用筛选
const applyFilter = () => {
  showFilterPopup.value = false
  loadProductList(true)
}

// 跳转到商品详情
const goToDetail = (productId: string) => {
  uni.navigateTo({
    url: `/pages/product/detail/index?id=${productId}`
  })
}
</script>

<style lang="scss" scoped>
.product-list-page {
  min-height: 100vh;
  background-color: $bg-color-page;
}

.filter-bar {
  display: flex;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;
  
  .filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    
    .filter-text {
      font-size: $font-size-base;
      color: $text-color-secondary;
      margin-right: 8rpx;
    }
    
    .iconfont {
      font-size: 24rpx;
      color: $text-color-placeholder;
    }
    
    &:active {
      background-color: $bg-color-gray;
    }
  }
}

.product-list {
  padding: $spacing-md;
}

.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
}

.loading-more,
.no-more {
  text-align: center;
  padding: $spacing-lg;
  color: $text-color-placeholder;
  font-size: $font-size-sm;
}

.empty-state {
  text-align: center;
  padding: 120rpx $spacing-md;
  
  .empty-text {
    color: $text-color-placeholder;
    font-size: $font-size-base;
  }
}

// 弹窗样式
.sort-popup,
.filter-popup {
  background-color: $bg-color-white;
  border-radius: 16rpx 16rpx 0 0;
  
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid $border-color-light;
    
    .popup-title {
      font-size: $font-size-large;
      font-weight: 600;
      color: $text-color-primary;
    }
    
    .popup-close {
      font-size: 48rpx;
      color: $text-color-placeholder;
    }
  }
}

.sort-options {
  padding: $spacing-md 0;
  
  .sort-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-md $spacing-lg;
    
    &.active {
      color: $primary-color;
      
      .iconfont {
        color: $primary-color;
      }
    }
    
    &:active {
      background-color: $bg-color-gray;
    }
  }
}

.filter-popup {
  width: 600rpx;
  height: 100vh;
}

.filter-section {
  padding: $spacing-lg;
  border-bottom: 1px solid $border-color-light;
  
  .section-title {
    font-size: $font-size-base;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-md;
  }
}

.price-range {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  
  .price-input {
    flex: 1;
    height: 72rpx;
    padding: 0 $spacing-md;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    font-size: $font-size-base;
  }
  
  .price-separator {
    color: $text-color-placeholder;
  }
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;
  
  .category-item {
    padding: $spacing-sm $spacing-md;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    font-size: $font-size-sm;
    color: $text-color-secondary;
    
    &.active {
      border-color: $primary-color;
      color: $primary-color;
      background-color: rgba($primary-color, 0.1);
    }
  }
}

.filter-actions {
  display: flex;
  gap: $spacing-md;
  padding: $spacing-lg;
  
  .mall-button {
    flex: 1;
  }
}
</style>
