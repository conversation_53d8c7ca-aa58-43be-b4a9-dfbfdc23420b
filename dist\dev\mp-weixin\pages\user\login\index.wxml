<view class="login-page data-v-e19616f2"><view class="login-header data-v-e19616f2"><view class="header-bg data-v-e19616f2"></view><view class="logo-section data-v-e19616f2"><image src="{{a}}" class="app-logo data-v-e19616f2" mode="aspectFit"/><text class="app-name data-v-e19616f2">商城小程序</text></view></view><view class="login-form data-v-e19616f2"><view class="form-title data-v-e19616f2"><text class="title-text data-v-e19616f2">欢迎登录</text><text class="subtitle-text data-v-e19616f2">请使用微信授权登录</text></view><view class="wechat-login data-v-e19616f2"><button class="wechat-login-btn data-v-e19616f2" open-type="getUserInfo" bindgetuserinfo="{{b}}" loading="{{c}}"><text class="iconfont icon-wechat data-v-e19616f2"></text><text class="btn-text data-v-e19616f2">微信授权登录</text></button><view class="login-tips data-v-e19616f2"><text class="tips-text data-v-e19616f2">登录即表示同意</text><text class="link-text data-v-e19616f2" bindtap="{{d}}">《隐私政策》</text><text class="tips-text data-v-e19616f2">和</text><text class="link-text data-v-e19616f2" bindtap="{{e}}">《用户协议》</text></view></view><view class="phone-login data-v-e19616f2"><view class="form-item data-v-e19616f2"><text class="item-label data-v-e19616f2">手机号</text><input type="number" placeholder="请输入手机号" class="form-input data-v-e19616f2" maxlength="11" value="{{f}}" bindinput="{{g}}"/></view><view class="form-item data-v-e19616f2"><text class="item-label data-v-e19616f2">验证码</text><view class="code-input-group data-v-e19616f2"><input type="number" placeholder="请输入验证码" class="form-input data-v-e19616f2" maxlength="6" value="{{h}}" bindinput="{{i}}"/><mall-button wx:if="{{l}}" class="data-v-e19616f2" u-s="{{['d']}}" bindclick="{{k}}" u-i="e19616f2-0" bind:__l="__l" u-p="{{l}}">{{j}}</mall-button></view></view><mall-button wx:if="{{n}}" class="data-v-e19616f2" u-s="{{['d']}}" bindclick="{{m}}" u-i="e19616f2-1" bind:__l="__l" u-p="{{n}}"> 手机号登录 </mall-button></view><view class="login-switch data-v-e19616f2"><text class="switch-text data-v-e19616f2" bindtap="{{p}}">{{o}}</text></view></view><view class="guest-mode data-v-e19616f2"><text class="guest-text data-v-e19616f2" bindtap="{{q}}">暂不登录，先逛逛</text></view></view>