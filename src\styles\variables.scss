// 主题色彩
$primary-color: #ff6b35;
$primary-light: #ff8a5c;
$primary-dark: #e55a2b;

// 辅助色彩
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

// 文字颜色
$text-color-primary: #333333;
$text-color-secondary: #666666;
$text-color-regular: #666666;  // 添加缺失的变量，与 secondary 保持一致
$text-color-placeholder: #999999;
$text-color-disabled: #cccccc;
$text-color-white: #ffffff;

// 背景色
$bg-color-page: #f8f8f8;
$bg-color-white: #ffffff;
$bg-color-gray: #f5f5f5;
$bg-color-dark: #333333;

// 边框色
$border-color-light: #f0f0f0;
$border-color-base: #d9d9d9;
$border-color-dark: #999999;

// 阴影
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
$box-shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.25);

// 圆角
$border-radius-small: 4px;
$border-radius-base: 8px;
$border-radius-large: 12px;
$border-radius-round: 50%;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-small: 8px;  // 添加 small 别名
$spacing-md: 12px;
$spacing-base: 12px;  // 添加 base 别名
$spacing-lg: 16px;
$spacing-large: 16px;  // 添加 large 别名
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 字体大小
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-small: 12px;  // 添加 small 别名
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-large: 16px;  // 添加 large 别名
$font-size-xl: 18px;
$font-size-xxl: 20px;
$font-size-title: 24px;

// 行高
$line-height-base: 1.5;
$line-height-sm: 1.2;
$line-height-lg: 1.8;

// 字体粗细
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 600;

// Z-index层级
$z-index-base: 1;
$z-index-popup: 1000;
$z-index-modal: 1500;
$z-index-toast: 2000;
$z-index-loading: 2500;

// 动画时间
$animation-duration-fast: 0.2s;
$animation-duration-base: 0.3s;
$animation-duration-slow: 0.5s;

// 屏幕断点
$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1200px;
