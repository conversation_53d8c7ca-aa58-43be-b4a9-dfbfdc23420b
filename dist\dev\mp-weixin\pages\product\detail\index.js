"use strict";var e=(e,t,a)=>new Promise((i,n)=>{var o=e=>{try{r(a.next(e))}catch(t){n(t)}},s=e=>{try{r(a.throw(e))}catch(t){n(t)}},r=e=>e.done?i(e.value):Promise.resolve(e.value).then(o,s);r((a=a.apply(e,t)).next())});const t=require("../../../common/vendor.js");if(!Array){t.resolveComponent("mall-button")()}Math;const a=t.defineComponent({__name:"index",setup(a){const i=t.ref(""),n=t.ref({id:"",name:"",description:"",price:0,originalPrice:0,images:[],categoryId:"",categoryName:"",stock:0,sales:0,rating:0,reviewCount:0,tags:[],specs:[],status:"active",createdAt:"",updatedAt:""}),o=t.ref([]),s=t.ref({}),r=t.ref(!1),c=t.ref(0);t.onLoad(e=>{(null==e?void 0:e.id)&&(i.value=e.id,l(),u(),d())});const l=()=>e(this,null,function*(){try{n.value={id:i.value,name:"iPhone 15 Pro Max 256GB",description:"全新iPhone 15 Pro Max，搭载A17 Pro芯片，支持5G网络，拍照更清晰，续航更持久。采用钛金属材质，更轻更坚固。",price:9999,originalPrice:10999,images:["https://picsum.photos/400/400?random=1","https://picsum.photos/400/400?random=2","https://picsum.photos/400/400?random=3"],categoryId:"1",categoryName:"手机数码",stock:50,sales:1288,rating:4.8,reviewCount:256,tags:["热销","新品","5G"],specs:[{id:"color",name:"颜色",options:[{id:"black",name:"深空黑",value:"black",stock:20},{id:"white",name:"银色",value:"white",stock:15},{id:"gold",name:"金色",value:"gold",stock:10}]},{id:"storage",name:"存储容量",options:[{id:"256gb",name:"256GB",value:"256gb",stock:30,price:0},{id:"512gb",name:"512GB",value:"512gb",stock:20,price:1e3},{id:"1tb",name:"1TB",value:"1tb",stock:10,price:2e3}]}],status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},n.value.specs&&n.value.specs.forEach(e=>{e.options.length>0&&(s.value[e.id]=e.options[0].id)})}catch(e){t.index.showToast({title:"加载失败",icon:"none"})}}),u=()=>e(this,null,function*(){try{o.value=[{id:"1",userId:"1",user:{nickname:"用户***123",avatar:"https://picsum.photos/60/60?random=10"},productId:i.value,orderId:"1",rating:5,content:"手机很不错，拍照清晰，运行流畅，值得购买！",images:[],createdAt:(new Date).toISOString()},{id:"2",userId:"2",user:{nickname:"用户***456",avatar:"https://picsum.photos/60/60?random=11"},productId:i.value,orderId:"2",rating:4,content:"整体满意，就是价格有点贵，但是质量确实好。",images:[],createdAt:(new Date).toISOString()}]}catch(e){}}),d=()=>e(this,null,function*(){try{c.value=3}catch(e){}}),v=()=>{r.value=!r.value,t.index.showToast({title:r.value?"已收藏":"已取消收藏",icon:"none"})},p=()=>{t.index.switchTab({url:"/pages/cart/index"})},g=()=>{t.index.navigateTo({url:`/pages/product/reviews/index?productId=${i.value}`})},m=()=>{if(n.value.specs&&n.value.specs.length>0){const e=n.value.specs.filter(e=>!s.value[e.id]);if(e.length>0)return void t.index.showToast({title:`请选择${e[0].name}`,icon:"none"})}c.value++,t.index.showToast({title:"已加入购物车",icon:"success"})},h=()=>{if(n.value.specs&&n.value.specs.length>0){const e=n.value.specs.filter(e=>!s.value[e.id]);if(e.length>0)return void t.index.showToast({title:`请选择${e[0].name}`,icon:"none"})}t.index.navigateTo({url:`/pages/order/confirm/index?productId=${i.value}&specs=${JSON.stringify(s.value)}`})};return(e,a)=>t.e({a:t.f(n.value.images,(e,t,a)=>({a:e,b:t})),b:t.t(n.value.price),c:n.value.originalPrice},n.value.originalPrice?{d:t.t(n.value.originalPrice)}:{},{e:t.t(n.value.name),f:t.f(n.value.tags,(e,a,i)=>({a:t.t(e),b:e})),g:t.t(n.value.sales),h:t.t(n.value.stock),i:t.t(n.value.reviewCount),j:n.value.specs&&n.value.specs.length>0},n.value.specs&&n.value.specs.length>0?{k:t.f(n.value.specs,(e,a,i)=>({a:t.t(e.name),b:t.f(e.options,(a,i,o)=>({a:t.t(a.name),b:a.id,c:s.value[e.id]===a.id?1:"",d:0===a.stock?1:"",e:t.o(t=>((e,t)=>{var a;const i=null==(a=n.value.specs)?void 0:a.find(t=>t.id===e),o=null==i?void 0:i.options.find(e=>e.id===t);o&&o.stock>0&&(s.value[e]=t)})(e.id,a.id),a.id)})),c:e.id}))}:{},{l:t.t(n.value.description),m:t.t(n.value.reviewCount),n:t.o(g),o:o.value.length>0},o.value.length>0?{p:t.f(o.value.slice(0,2),(e,a,i)=>({a:e.user.avatar,b:t.t(e.user.nickname),c:t.f(5,(t,a,i)=>({a:t,b:t<=e.rating?1:""})),d:t.t(e.content),e:e.id}))}:{},{q:t.n(r.value?"icon-heart-fill":"icon-heart"),r:t.o(v),s:c.value>0},c.value>0?{t:t.t(c.value)}:{},{v:t.o(p),w:t.o(m),x:t.p({type:"warning"}),y:t.o(h),z:t.p({type:"primary"})})}}),i=t._export_sfc(a,[["__scopeId","data-v-96b80d98"]]);wx.createPage(i);
