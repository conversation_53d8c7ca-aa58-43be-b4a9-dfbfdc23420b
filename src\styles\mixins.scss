// 文本省略号
@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略号
@mixin ellipsis-lines($lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 居中对齐
@mixin center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 响应式断点
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

// 按钮样式
@mixin button-style($bg-color, $text-color: #fff, $border-color: $bg-color) {
  background-color: $bg-color;
  color: $text-color;
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-base;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    transform: translateY(1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card {
  background-color: $bg-color-white;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
  padding: $spacing-lg;
}

// 输入框样式
@mixin input-style {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: $bg-color-white;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
  }
  
  &::placeholder {
    color: $text-color-placeholder;
  }
  
  &:disabled {
    background-color: $bg-color-gray;
    color: $text-color-disabled;
    cursor: not-allowed;
  }
}
