"use strict";var e=(e,t,a)=>new Promise((n,o)=>{var l=e=>{try{s(a.next(e))}catch(t){o(t)}},i=e=>{try{s(a.throw(e))}catch(t){o(t)}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(l,i);s((a=a.apply(e,t)).next())});const t=require("../../../common/vendor.js");if(!Array){(t.resolveComponent("mall-navbar")+t.resolveComponent("mall-button"))()}Math||((()=>"../../../components/mall-navbar/mall-navbar.js")+(()=>"../../../components/mall-button/mall-button.js"))();const a=t.defineComponent({__name:"index",setup(a){const n=t.ref("all"),o=t.ref([]),l=t.ref("all"),i=t.ref(!1),s=t.ref(!0),d=t.ref(1),u=t.ref([{label:"全部",value:"all",count:0},{label:"待付款",value:"pending",count:0},{label:"待发货",value:"paid",count:0},{label:"待收货",value:"shipped",count:0},{label:"已完成",value:"completed",count:0}]);t.onLoad(e=>{(null==e?void 0:e.status)&&(n.value=e.status,l.value=e.status)}),t.onMounted(()=>{r(!0),c()}),t.onReachBottom(()=>{!i.value&&s.value&&r(!1)});const r=(a=!1)=>e(this,null,function*(){if(!i.value){i.value=!0;try{a&&(d.value=1,o.value=[]);const e=p(d.value,10,l.value);a?o.value=e:o.value.push(...e),s.value=10===e.length,d.value++}catch(e){t.index.showToast({title:"加载失败",icon:"none"})}finally{i.value=!1}}}),c=()=>e(this,null,function*(){try{const e={all:15,pending:2,paid:3,shipped:5,completed:5};u.value.forEach(t=>{t.count=e[t.value]||0})}catch(e){}}),p=(e,t,a)=>{const n=[],o=(e-1)*t,l=["pending","paid","shipped","completed"];for(let i=0;i<t;i++){const e=o+i+1,t="all"===a?l[i%l.length]:a;n.push({id:e.toString(),orderNo:`ORDER${Date.now()}${e}`,userId:"1",status:t,items:[{id:`${e}_1`,productId:`product_${e}`,productName:`商品名称 ${e}`,productImage:`https://picsum.photos/120/120?random=${e}`,quantity:Math.floor(3*Math.random())+1,price:Math.floor(1e3*Math.random())+100,totalPrice:0,selectedSpecs:[{specId:"color",specName:"颜色",optionId:"red",optionName:"红色",optionValue:"red"}]}],totalAmount:Math.floor(2e3*Math.random())+500,discountAmount:Math.floor(100*Math.random()),shippingFee:10,payAmount:0,payMethod:"wechat",payTime:"pending"!==t?(new Date).toISOString():void 0,shippingAddress:{id:"1",userId:"1",name:"张三",phone:"13800138000",province:"广东省",city:"深圳市",district:"南山区",detail:"科技园南区深南大道xxx号",isDefault:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},remark:i%3==0?"请尽快发货":void 0,createdAt:new Date(Date.now()-24*i*60*60*1e3).toISOString(),updatedAt:(new Date).toISOString()}),n[i].payAmount=n[i].totalAmount+n[i].shippingFee-n[i].discountAmount,n[i].items[0].totalPrice=n[i].items[0].price*n[i].items[0].quantity}return n},v=e=>({pending:"status-pending",paid:"status-paid",shipped:"status-shipped",delivered:"status-delivered",completed:"status-completed",cancelled:"status-cancelled",refunding:"status-refunding",refunded:"status-refunded"}[e]||""),m=e=>{const t=new Date(e),a=(new Date).getTime()-t.getTime(),n=Math.floor(a/864e5);return 0===n?"今天":1===n?"昨天":n<7?`${n}天前`:t.toLocaleDateString()},h=e=>{const t=[];switch(e.status){case"pending":t.push({type:"cancel",text:"取消订单",buttonType:"default"},{type:"pay",text:"立即付款",buttonType:"primary"});break;case"paid":t.push({type:"remind",text:"提醒发货",buttonType:"default"});break;case"shipped":t.push({type:"logistics",text:"查看物流",buttonType:"default"},{type:"confirm",text:"确认收货",buttonType:"primary"});break;case"delivered":t.push({type:"review",text:"评价",buttonType:"primary"});break;case"completed":t.push({type:"buy-again",text:"再次购买",buttonType:"default"})}return t};return(e,a)=>t.e({a:t.p({title:"我的订单","show-back":!0}),b:t.f(u.value,(e,a,n)=>t.e({a:t.t(e.label),b:e.count>0},e.count>0?{c:t.t(e.count)}:{},{d:e.value,e:l.value===e.value?1:"",f:t.o(t=>{return a=e.value,void(l.value!==a&&(l.value=a,r(!0)));var a},e.value)})),c:t.f(o.value,(e,a,n)=>{return{a:t.t(e.orderNo),b:t.t((l=e.status,{pending:"待付款",paid:"待发货",shipped:"待收货",delivered:"待评价",completed:"已完成",cancelled:"已取消",refunding:"退款中",refunded:"已退款"}[l]||"未知状态")),c:t.n(v(e.status)),d:t.f(e.items,(a,n,o)=>t.e({a:a.productImage,b:t.t(a.productName),c:a.selectedSpecs.length>0},a.selectedSpecs.length>0?{d:t.f(a.selectedSpecs,(e,a,n)=>({a:t.t(e.specName),b:t.t(e.optionName),c:e.specId}))}:{},{e:t.t(a.price),f:t.t(a.quantity),g:a.id,h:t.o(a=>{return n=e.id,void t.index.navigateTo({url:`/pages/order/detail/index?id=${n}`});var n},a.id)})),e:t.t(m(e.createdAt)),f:t.t((o=e.items,o.reduce((e,t)=>e+t.quantity,0))),g:t.t(e.payAmount),h:t.f(h(e),(a,o,l)=>({a:t.t(a.text),b:a.type,c:t.o(n=>((e,a)=>{switch(e){case"pay":t.index.navigateTo({url:`/pages/order/pay/index?orderId=${a.id}&amount=${a.payAmount}`});break;case"cancel":t.index.showModal({title:"确认取消",content:"确定要取消这个订单吗？",success:e=>{e.confirm&&(t.index.showToast({title:"订单已取消",icon:"success"}),r(!0))}});break;case"confirm":t.index.showModal({title:"确认收货",content:"确定已收到商品吗？",success:e=>{e.confirm&&(t.index.showToast({title:"确认收货成功",icon:"success"}),r(!0))}});break;case"logistics":t.index.navigateTo({url:`/pages/order/logistics/index?orderId=${a.id}`});break;case"review":t.index.navigateTo({url:`/pages/order/review/index?orderId=${a.id}`});break;case"buy-again":t.index.showToast({title:"已加入购物车",icon:"success"});break;default:t.index.showToast({title:"功能开发中",icon:"none"})}})(a.type,e),a.type),d:"50fcc5e9-1-"+n+"-"+l,e:t.p({type:a.buttonType,size:"small"})})),i:e.id};var o,l}),d:i.value},(i.value||!s.value&&o.value.length>0||0!==o.value.length||i.value,{}),{e:!s.value&&o.value.length>0,f:0===o.value.length&&!i.value})}}),n=t._export_sfc(a,[["__scopeId","data-v-50fcc5e9"]]);wx.createPage(n);
