<template>
  <button
    class="mall-button"
    :class="buttonClass"
    :disabled="disabled || loading"
    :form-type="formType"
    :open-type="openType"
    @click="handleClick"
    @getuserinfo="handleGetUserInfo"
    @contact="handleContact"
    @getphonenumber="handleGetPhoneNumber"
    @error="handleError"
    @opensetting="handleOpenSetting"
    @launchapp="handleLaunchApp"
  >
    <view v-if="loading" class="mall-button__loading">
      <view class="loading-spinner"></view>
    </view>
    <view v-if="icon && !loading" class="mall-button__icon">
      <image :src="icon" class="icon-image" mode="aspectFit" />
    </view>
    <view class="mall-button__text">
      <slot>{{ text }}</slot>
    </view>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'text'
  size?: 'mini' | 'small' | 'medium' | 'large'
  shape?: 'square' | 'round'
  disabled?: boolean
  loading?: boolean
  block?: boolean
  text?: string
  icon?: string
  formType?: 'submit' | 'reset'
  openType?: 'contact' | 'share' | 'getPhoneNumber' | 'getUserInfo' | 'launchApp' | 'openSetting' | 'feedback'
}

interface Emits {
  (e: 'click', event: Event): void
  (e: 'getuserinfo', event: any): void
  (e: 'contact', event: any): void
  (e: 'getphonenumber', event: any): void
  (e: 'error', event: any): void
  (e: 'opensetting', event: any): void
  (e: 'launchapp', event: any): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  shape: 'square',
  disabled: false,
  loading: false,
  block: false,
  text: ''
})

const emit = defineEmits<Emits>()

const buttonClass = computed(() => {
  return [
    `mall-button--${props.type}`,
    `mall-button--${props.size}`,
    `mall-button--${props.shape}`,
    {
      'mall-button--block': props.block,
      'mall-button--disabled': props.disabled,
      'mall-button--loading': props.loading
    }
  ]
})

const handleClick = (event: Event) => {
  if (props.disabled || props.loading) return
  emit('click', event)
}

const handleGetUserInfo = (event: any) => {
  emit('getuserinfo', event)
}

const handleContact = (event: any) => {
  emit('contact', event)
}

const handleGetPhoneNumber = (event: any) => {
  emit('getphonenumber', event)
}

const handleError = (event: any) => {
  emit('error', event)
}

const handleOpenSetting = (event: any) => {
  emit('opensetting', event)
}

const handleLaunchApp = (event: any) => {
  emit('launchapp', event)
}
</script>

<style lang="scss" scoped>
@use "sass:color";
.mall-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all $animation-duration-base;
  font-weight: $font-weight-medium;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  
  &::after {
    border: none;
  }
  
  // 类型样式
  &--primary {
    background-color: $primary-color;
    color: $text-color-white;
    
    &:not(.mall-button--disabled):active {
      background-color: $primary-dark;
    }
  }
  
  &--secondary {
    background-color: $bg-color-white;
    color: $primary-color;
    border: 1px solid $primary-color;
    
    &:not(.mall-button--disabled):active {
      background-color: rgba($primary-color, 0.1);
    }
  }
  
  &--success {
    background-color: $success-color;
    color: $text-color-white;
    
    &:not(.mall-button--disabled):active {
      background-color: color.adjust($success-color, $lightness: -10%);
    }
  }
  
  &--warning {
    background-color: $warning-color;
    color: $text-color-white;
    
    &:not(.mall-button--disabled):active {
      background-color: color.adjust($warning-color, $lightness: -10%);
    }
  }
  
  &--error {
    background-color: $error-color;
    color: $text-color-white;
    
    &:not(.mall-button--disabled):active {
      background-color: color.adjust($error-color, $lightness: -10%);
    }
  }
  
  &--text {
    background-color: transparent;
    color: $primary-color;
    
    &:not(.mall-button--disabled):active {
      background-color: rgba($primary-color, 0.1);
    }
  }
  
  // 尺寸样式
  &--mini {
    height: 24px;
    padding: 0 $spacing-xs;
    font-size: $font-size-xs;
    border-radius: $border-radius-small;
  }
  
  &--small {
    height: 32px;
    padding: 0 $spacing-sm;
    font-size: $font-size-sm;
    border-radius: $border-radius-small;
  }
  
  &--medium {
    height: 40px;
    padding: 0 $spacing-lg;
    font-size: $font-size-base;
    border-radius: $border-radius-base;
  }
  
  &--large {
    height: 48px;
    padding: 0 $spacing-xl;
    font-size: $font-size-lg;
    border-radius: $border-radius-base;
  }
  
  // 形状样式
  &--round {
    border-radius: 999px;
  }
  
  // 块级按钮
  &--block {
    width: 100%;
    display: flex;
  }
  
  // 禁用状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 加载状态
  &--loading {
    cursor: not-allowed;
  }
  
  &__loading {
    margin-right: $spacing-xs;
    
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
  
  &__icon {
    margin-right: $spacing-xs;
    
    .icon-image {
      width: 16px;
      height: 16px;
    }
  }
  
  &__text {
    flex: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
