<template>
  <view id="app">
    <!-- 全局加载提示 -->
    <view v-if="globalLoading" class="global-loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useAppStore } from '@/store/app'

const appStore = useAppStore()
const globalLoading = ref(false)

onLaunch(() => {
  console.log('App Launch')
  // 初始化应用
  initApp()
})

onShow(() => {
  console.log('App Show')
  // 应用显示时的逻辑
})

onHide(() => {
  console.log('App Hide')
  // 应用隐藏时的逻辑
})

const initApp = async () => {
  try {
    globalLoading.value = true
    
    // 获取系统信息
    const systemInfo = await uni.getSystemInfo()
    appStore.setSystemInfo(systemInfo)
    
    // 检查更新
    checkUpdate()
    
    // 其他初始化逻辑
    
  } catch (error) {
    console.error('App初始化失败:', error)
  } finally {
    globalLoading.value = false
  }
}

const checkUpdate = () => {
  // #ifdef MP-WEIXIN
  if (uni.canIUse('getUpdateManager')) {
    const updateManager = uni.getUpdateManager()
    
    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        updateManager.onUpdateReady(() => {
          uni.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success: (res) => {
              if (res.confirm) {
                updateManager.applyUpdate()
              }
            }
          })
        })
        
        updateManager.onUpdateFailed(() => {
          uni.showModal({
            title: '更新失败',
            content: '新版本下载失败，请检查网络后重试',
            showCancel: false
          })
        })
      }
    })
  }
  // #endif
}
</script>

<style lang="scss">
/* 全局样式 */
@use '@/styles/variables.scss' as *;
@use '@/styles/common.scss' as *;

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .loading-text {
    margin-top: 16px;
    color: $text-color-secondary;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
