"use strict";var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,i=(a,t,n)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):a[t]=n,l=(e,a)=>{for(var t in a||(a={}))r.call(a,t)&&i(e,t,a[t]);if(n)for(var t of n(a))o.call(a,t)&&i(e,t,a[t]);return e},c=(e,n)=>a(e,t(n));const u=require("../../../common/vendor.js");if(!Array){(u.resolveComponent("mall-navbar")+u.resolveComponent("mall-button")+u.resolveComponent("uni-popup"))()}Math||((()=>"../../../components/mall-navbar/mall-navbar.js")+(()=>"../../../components/mall-button/mall-button.js"))();const s=u.defineComponent({__name:"index",setup(e){const a=u.ref({id:"",nickname:"",avatar:"",phone:"",gender:0,birthday:"",level:1,points:0,balance:0,status:"active",createdAt:"",updatedAt:""}),t=u.reactive({nickname:"",gender:0,birthday:""}),n=u.ref(!1),r=u.ref(!1),o=[{label:"未知",value:0},{label:"男",value:1},{label:"女",value:2}];u.onMounted(()=>{i()});const i=()=>{try{const e=u.index.getStorageSync("userInfo");e&&(a.value=c(l({},e),{birthday:e.birthday||""}),t.nickname=a.value.nickname,t.gender=a.value.gender,t.birthday=a.value.birthday||"")}catch(e){}},s=()=>{u.index.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:e=>{const t=e.tempFilePaths[0];a.value.avatar=t,u.index.showToast({title:"头像已更新",icon:"success"})},fail:e=>{}})},d=()=>{t.nickname=a.value.nickname,u.index.showModal({title:"修改昵称",editable:!0,placeholderText:"请输入昵称",success:e=>{e.confirm&&e.content&&(a.value.nickname=e.content.trim(),t.nickname=e.content.trim())}})},v=()=>{t.nickname.trim()?a.value.nickname=t.nickname.trim():u.index.showToast({title:"请输入昵称",icon:"none"})},p=()=>{},m=()=>{u.index.showActionSheet({itemList:["未知","男","女"],success:e=>{a.value.gender=e.tapIndex,t.gender=e.tapIndex}})},b=()=>{},y=()=>{r.value=!0},h=e=>{const n=e.detail.value;a.value.birthday=n,t.birthday=n,r.value=!1},g=()=>{u.index.navigateTo({url:"/pages/user/phone/index"})},f=()=>{return e=this,r=null,o=function*(){n.value=!0;try{yield new Promise(e=>setTimeout(e,1e3));const e=c(l({},a.value),{nickname:t.nickname||a.value.nickname,gender:t.gender,birthday:t.birthday,updatedAt:(new Date).toISOString()});u.index.setStorageSync("userInfo",e),a.value=e,u.index.showToast({title:"保存成功",icon:"success"}),setTimeout(()=>{u.index.navigateBack()},1500)}catch(e){u.index.showToast({title:"保存失败，请重试",icon:"none"})}finally{n.value=!1}},new Promise((a,t)=>{var n=e=>{try{l(o.next(e))}catch(a){t(a)}},i=e=>{try{l(o.throw(e))}catch(a){t(a)}},l=e=>e.done?a(e.value):Promise.resolve(e.value).then(n,i);l((o=o.apply(e,r)).next())});var e,r,o};return(e,i)=>{return u.e({a:u.p({title:"个人资料","show-back":!0}),b:a.value.avatar,c:u.o(s),d:u.t(a.value.nickname),e:u.o(d),f:u.t((l=a.value.gender,{0:"未知",1:"男",2:"女"}[l])),g:u.o(m),h:u.t(a.value.birthday||"未设置"),i:u.o(y),j:u.t(a.value.phone||"未绑定"),k:u.o(g),l:u.o(f),m:u.p({type:"primary",loading:n.value}),n:t.nickname,o:u.o(e=>t.nickname=e.detail.value),p:u.o(p),q:u.p({type:"default"}),r:u.o(v),s:u.p({type:"primary"}),t:u.sr("nicknamePopup","7aedad92-2"),v:u.p({type:"center"}),w:u.o(b),x:u.f(o,(e,n,r)=>u.e({a:u.t(e.label),b:t.gender===e.value},(t.gender,e.value,{}),{c:e.value,d:t.gender===e.value?1:"",e:u.o(n=>{return r=e.value,t.gender=r,void(a.value.gender=r);var r},e.value)})),y:u.sr("genderPopup","7aedad92-5"),z:u.p({type:"bottom"}),A:r.value},r.value?{B:t.birthday,C:u.o(h),D:u.o(e=>r.value=!1)}:{});var l}}}),d=u._export_sfc(s,[["__scopeId","data-v-7aedad92"]]);wx.createPage(d);
