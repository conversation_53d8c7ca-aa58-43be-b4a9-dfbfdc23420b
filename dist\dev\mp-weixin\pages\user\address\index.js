"use strict";var e=(e,t,a)=>new Promise((i,n)=>{var o=e=>{try{d(a.next(e))}catch(t){n(t)}},l=e=>{try{d(a.throw(e))}catch(t){n(t)}},d=e=>e.done?i(e.value):Promise.resolve(e.value).then(o,l);d((a=a.apply(e,t)).next())});const t=require("../../../common/vendor.js");if(!Array){(t.resolveComponent("mall-navbar")+t.resolveComponent("mall-button"))()}Math||((()=>"../../../components/mall-navbar/mall-navbar.js")+(()=>"../../../components/mall-button/mall-button.js"))();const a=t.defineComponent({__name:"index",setup(a){const i=t.ref(!1),n=t.ref([]),o=t.ref("");t.onLoad(e=>{"true"===(null==e?void 0:e.select)&&(i.value=!0)}),t.onMounted(()=>{l()});const l=()=>e(this,null,function*(){try{if(n.value=[{id:"1",userId:"1",name:"张三",phone:"13800138000",province:"广东省",city:"深圳市",district:"南山区",detail:"科技园南区深南大道xxx号",isDefault:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"2",userId:"1",name:"李四",phone:"13900139000",province:"北京市",city:"北京市",district:"朝阳区",detail:"三里屯街道xxx号",isDefault:!1,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"3",userId:"1",name:"王五",phone:"13700137000",province:"上海市",city:"上海市",district:"浦东新区",detail:"陆家嘴金融区xxx号",isDefault:!1,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()}],i.value){const e=n.value.find(e=>e.isDefault);e&&(o.value=e.id)}}catch(e){t.index.showToast({title:"加载失败",icon:"none"})}}),d=()=>{t.index.navigateTo({url:"/pages/user/address/edit/index"})},r=e=>{t.index.navigateTo({url:`/pages/user/address/edit/index?id=${e.id}`})},s=a=>{t.index.showModal({title:"确认删除",content:"确定要删除这个地址吗？",success:i=>e(this,null,function*(){if(i.confirm)try{yield new Promise(e=>setTimeout(e,500)),n.value=n.value.filter(e=>e.id!==a),t.index.showToast({title:"删除成功",icon:"success"})}catch(e){t.index.showToast({title:"删除失败",icon:"none"})}})})},u=()=>{if(!o.value)return;const e=n.value.find(e=>e.id===o.value);e&&(t.index.$emit("addressSelected",e),t.index.navigateBack())};return onShow(()=>{l()}),(e,a)=>t.e({a:t.p({title:i.value?"选择地址":"收货地址","show-back":!0}),b:t.f(n.value,(e,a,n)=>t.e({a:t.t(e.name),b:t.t(e.phone),c:e.isDefault},(e.isDefault,{}),{d:t.t(e.province),e:t.t(e.city),f:t.t(e.district),g:t.t(e.detail)},i.value?{}:{h:t.o(t=>r(e),e.id),i:t.o(t=>s(e.id),e.id)},i.value?{j:t.n(o.value===e.id?"icon-check-circle-fill":"icon-circle")}:{},{k:e.id,l:i.value&&o.value===e.id?1:"",m:t.o(t=>(e=>{i.value?o.value=e.id:r(e)})(e),e.id)})),c:!i.value,d:i.value,e:0===n.value.length},(n.value.length,{}),{f:t.o(d),g:t.p({type:"primary"}),h:i.value},i.value?{i:t.o(u),j:t.p({type:"primary",disabled:!o.value})}:{})}}),i=t._export_sfc(a,[["__scopeId","data-v-af14a249"]]);wx.createPage(i);
