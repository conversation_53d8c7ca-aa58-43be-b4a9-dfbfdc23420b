<template>
  <view class="coupon-page">
    <!-- 导航栏 -->
    <mall-navbar :title="isSelectMode ? '选择优惠券' : '我的优惠券'" :show-back="true" />
    
    <!-- 状态筛选标签 -->
    <view class="status-tabs">
      <view
        v-for="tab in statusTabs"
        :key="tab.value"
        class="status-tab"
        :class="{ active: currentStatus === tab.value }"
        @click="switchStatus(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</text>
      </view>
    </view>
    
    <!-- 优惠券列表 -->
    <view class="coupon-list">
      <view
        v-for="coupon in couponList"
        :key="coupon.id"
        class="coupon-item"
        :class="{ 
          expired: coupon.status === 'expired',
          used: coupon.status === 'used',
          active: isSelectMode && selectedCouponId === coupon.id,
          disabled: isSelectMode && !canUseCoupon(coupon)
        }"
        @click="handleCouponClick(coupon)"
      >
        <!-- 优惠券左侧 -->
        <view class="coupon-left">
          <view class="coupon-value">
            <text class="value-symbol">¥</text>
            <text class="value-amount">{{ coupon.coupon.value }}</text>
          </view>
          <text class="coupon-condition">
            满{{ coupon.coupon.minAmount }}元可用
          </text>
        </view>
        
        <!-- 优惠券右侧 -->
        <view class="coupon-right">
          <text class="coupon-name">{{ coupon.coupon.name }}</text>
          <text v-if="coupon.coupon.description" class="coupon-desc">
            {{ coupon.coupon.description }}
          </text>
          <text class="coupon-time">
            {{ formatCouponTime(coupon.coupon.startTime, coupon.coupon.endTime) }}
          </text>
          
          <!-- 状态标签 -->
          <view class="coupon-status">
            <text v-if="coupon.status === 'used'" class="status-tag used">已使用</text>
            <text v-else-if="coupon.status === 'expired'" class="status-tag expired">已过期</text>
            <text v-else-if="isSelectMode && !canUseCoupon(coupon)" class="status-tag disabled">不可用</text>
          </view>
        </view>
        
        <!-- 选择模式的选中状态 -->
        <view v-if="isSelectMode && canUseCoupon(coupon)" class="select-indicator">
          <text class="iconfont" :class="selectedCouponId === coupon.id ? 'icon-check-circle-fill' : 'icon-circle'"></text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="couponList.length === 0" class="empty-state">
        <text class="empty-text">暂无优惠券</text>
        <text class="empty-desc">快去商城逛逛，领取优惠券吧</text>
      </view>
    </view>
    
    <!-- 选择模式的底部操作 -->
    <view v-if="isSelectMode" class="bottom-actions">
      <view class="action-left">
        <text class="no-coupon-btn" @click="selectNoCoupon">不使用优惠券</text>
      </view>
      <view class="action-right">
        <mall-button
          type="primary"
          :disabled="!selectedCouponId"
          @click="confirmSelection"
        >
          确认使用
        </mall-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import type { UserCoupon } from '@/types'

// 页面参数
const isSelectMode = ref(false)
const orderAmount = ref(0)

// 响应式数据
const couponList = ref<UserCoupon[]>([])
const currentStatus = ref<'unused' | 'used' | 'expired'>('unused')
const selectedCouponId = ref('')

// 状态标签
const statusTabs = ref([
  { label: '未使用', value: 'unused' as const, count: 0 },
  { label: '已使用', value: 'used' as const, count: 0 },
  { label: '已过期', value: 'expired' as const, count: 0 }
])

// 页面生命周期
onLoad((options) => {
  if (options?.select === 'true') {
    isSelectMode.value = true
    if (options.amount) {
      orderAmount.value = Number(options.amount)
    }
  }
})

onMounted(() => {
  loadCouponList()
  loadCouponCounts()
})

// 加载优惠券列表
const loadCouponList = async () => {
  try {
    // 模拟优惠券数据
    const allCoupons: UserCoupon[] = [
      {
        id: '1',
        userId: '1',
        couponId: 'coupon_1',
        coupon: {
          id: 'coupon_1',
          name: '新用户专享券',
          type: 'amount',
          value: 50,
          minAmount: 200,
          startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          description: '新用户专享，满200减50'
        },
        status: 'unused',
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        userId: '1',
        couponId: 'coupon_2',
        coupon: {
          id: 'coupon_2',
          name: '满减优惠券',
          type: 'amount',
          value: 100,
          minAmount: 500,
          startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          description: '全场通用，满500减100'
        },
        status: 'unused',
        createdAt: new Date().toISOString()
      },
      {
        id: '3',
        userId: '1',
        couponId: 'coupon_3',
        coupon: {
          id: 'coupon_3',
          name: '限时优惠券',
          type: 'amount',
          value: 30,
          minAmount: 100,
          startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'expired',
          description: '限时活动，满100减30'
        },
        status: 'expired',
        createdAt: new Date().toISOString()
      },
      {
        id: '4',
        userId: '1',
        couponId: 'coupon_4',
        coupon: {
          id: 'coupon_4',
          name: '生日优惠券',
          type: 'amount',
          value: 20,
          minAmount: 50,
          startTime: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          description: '生日专享，满50减20'
        },
        status: 'used',
        usedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        orderId: 'order_123',
        createdAt: new Date().toISOString()
      }
    ]
    
    // 根据当前状态筛选
    couponList.value = allCoupons.filter(coupon => coupon.status === currentStatus.value)
  } catch (error) {
    console.error('加载优惠券列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 加载优惠券数量统计
const loadCouponCounts = async () => {
  try {
    // 模拟数量统计
    const counts = {
      unused: 2,
      used: 1,
      expired: 1
    }
    
    statusTabs.value.forEach(tab => {
      tab.count = counts[tab.value] || 0
    })
  } catch (error) {
    console.error('加载优惠券统计失败:', error)
  }
}

// 切换状态
const switchStatus = (status: 'unused' | 'used' | 'expired') => {
  if (currentStatus.value === status) return
  
  currentStatus.value = status
  loadCouponList()
}

// 判断优惠券是否可用
const canUseCoupon = (coupon: UserCoupon): boolean => {
  if (!isSelectMode.value) return true
  if (coupon.status !== 'unused') return false
  if (orderAmount.value < coupon.coupon.minAmount) return false
  
  const now = new Date()
  const startTime = new Date(coupon.coupon.startTime)
  const endTime = new Date(coupon.coupon.endTime)
  
  return now >= startTime && now <= endTime
}

// 处理优惠券点击
const handleCouponClick = (coupon: UserCoupon) => {
  if (isSelectMode.value) {
    if (canUseCoupon(coupon)) {
      selectedCouponId.value = selectedCouponId.value === coupon.id ? '' : coupon.id
    }
  }
}

// 格式化优惠券时间
const formatCouponTime = (startTime: string, endTime: string): string => {
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  const formatDate = (date: Date) => {
    return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`
  }
  
  return `${formatDate(start)} - ${formatDate(end)}`
}

// 不使用优惠券
const selectNoCoupon = () => {
  selectedCouponId.value = ''
  confirmSelection()
}

// 确认选择
const confirmSelection = () => {
  let selectedCoupon = null
  
  if (selectedCouponId.value) {
    selectedCoupon = couponList.value.find(coupon => coupon.id === selectedCouponId.value)
  }
  
  // 通过事件总线或其他方式传递选中的优惠券
  uni.$emit('couponSelected', selectedCoupon)
  
  // 返回上一页
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
@use "sass:color";
.coupon-page {
  min-height: 100vh;
  background-color: $bg-color-page;
  padding-bottom: 120rpx;
}

.status-tabs {
  display: flex;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;
  
  .status-tab {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    
    .tab-text {
      font-size: $font-size-base;
      color: $text-color-secondary;
    }
    
    .tab-badge {
      position: absolute;
      top: 16rpx;
      right: 20rpx;
      min-width: 32rpx;
      height: 32rpx;
      background-color: $error-color;
      color: $bg-color-white;
      font-size: 20rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8rpx;
    }
    
    &.active {
      .tab-text {
        color: $primary-color;
        font-weight: 600;
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: $primary-color;
        border-radius: 2rpx;
      }
    }
  }
}

.coupon-list {
  padding: $spacing-md;
}

.coupon-item {
  display: flex;
  align-items: center;
  background-color: $bg-color-white;
  border-radius: $border-radius-base;
  margin-bottom: $spacing-md;
  overflow: hidden;
  position: relative;
  
  &.expired,
  &.used {
    opacity: 0.6;
  }
  
  &.active {
    border: 2px solid $primary-color;
  }
  
  &.disabled {
    opacity: 0.4;
  }
  
  .coupon-left {
    width: 200rpx;
    background: linear-gradient(135deg, $primary-color 0%, color.adjust($primary-color, $lightness: 10%) 100%);
    color: $bg-color-white;
    padding: $spacing-lg;
    text-align: center;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      right: -10rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 20rpx;
      height: 20rpx;
      background-color: $bg-color-page;
      border-radius: 50%;
    }

    .coupon-value {
      display: flex;
      align-items: baseline;
      justify-content: center;
      margin-bottom: $spacing-sm;
      
      .value-symbol {
        font-size: 24rpx;
        margin-right: 4rpx;
      }
      
      .value-amount {
        font-size: 48rpx;
        font-weight: 600;
      }
    }
    
    .coupon-condition {
      font-size: $font-size-small;
      opacity: 0.9;
    }
  }
  
  .coupon-right {
    flex: 1;
    padding: $spacing-lg;
    position: relative;

    .coupon-name {
      font-size: $font-size-base;
      font-weight: 600;
      color: $text-color-primary;
      display: block;
      margin-bottom: $spacing-sm;
    }

    .coupon-desc {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      display: block;
      margin-bottom: $spacing-sm;
    }

    .coupon-time {
      font-size: $font-size-sm;
      color: $text-color-placeholder;
    }

    .coupon-status {
      position: absolute;
      top: $spacing-md;
      right: $spacing-md;

      .status-tag {
        padding: 4rpx $spacing-sm;
        font-size: $font-size-sm;
        border-radius: $border-radius-small;
        
        &.used {
          background-color: $text-color-placeholder;
          color: $bg-color-white;
        }
        
        &.expired {
          background-color: $error-color;
          color: $bg-color-white;
        }
        
        &.disabled {
          background-color: $border-color-base;
          color: $text-color-placeholder;
        }
      }
    }
  }
  
  .select-indicator {
    padding: $spacing-md;

    .iconfont {
      font-size: 48rpx;
      color: $primary-color;

      &.icon-circle {
        color: $border-color-base;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 120rpx $spacing-md;

  .empty-text {
    font-size: $font-size-lg;
    color: $text-color-placeholder;
    display: block;
    margin-bottom: $spacing-md;
  }

  .empty-desc {
    font-size: $font-size-base;
    color: $text-color-placeholder;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  padding: $spacing-md $spacing-lg;
  padding-bottom: calc($spacing-md + env(safe-area-inset-bottom));

  .action-left {
    .no-coupon-btn {
      font-size: $font-size-base;
      color: $text-color-secondary;
    }
  }

  .action-right {
    flex: 1;
    margin-left: $spacing-lg;
    
    .mall-button {
      width: 100%;
    }
  }
}
</style>
