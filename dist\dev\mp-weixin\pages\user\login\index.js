"use strict";var e=(e,t,n)=>new Promise((a,o)=>{var i=e=>{try{s(n.next(e))}catch(t){o(t)}},l=e=>{try{s(n.throw(e))}catch(t){o(t)}},s=e=>e.done?a(e.value):Promise.resolve(e.value).then(i,l);s((n=n.apply(e,t)).next())});const t=require("../../../common/vendor.js"),n=require("../../../common/assets.js");if(!Array){t.resolveComponent("mall-button")()}Math;const a=t.defineComponent({__name:"index",setup(a){const o=t.ref("wechat"),i=t.ref(!1),l=t.ref(!1),s=t.ref(!1),r=t.ref(0);let c=null;const u=t.ref({phone:"",code:""}),d=t.computed(()=>/^1[3-9]\d{9}$/.test(u.value.phone)),v=t.computed(()=>d.value&&6===u.value.code.length),h=n=>e(this,null,function*(){const{userInfo:e}=n.detail;if(e){i.value=!0;try{if(!(yield t.index.login({provider:"weixin"}))[1].code)throw new Error("获取登录凭证失败");yield new Promise(e=>setTimeout(e,1e3));const n={id:"user_"+Date.now(),nickname:e.nickName,avatar:e.avatarUrl,phone:"",gender:e.gender,openid:"mock_openid",token:"mock_token_"+Date.now()};t.index.setStorageSync("userInfo",n),t.index.setStorageSync("token",n.token),t.index.showToast({title:"登录成功",icon:"success"}),setTimeout(()=>{getCurrentPages().length>1?t.index.navigateBack():t.index.switchTab({url:"/pages/home/<USER>"})},1500)}catch(a){t.index.showToast({title:"登录失败，请重试",icon:"none"})}finally{i.value=!1}}else t.index.showToast({title:"授权失败，请重试",icon:"none"})}),m=()=>e(this,null,function*(){if(d.value&&!s.value){s.value=!0;try{yield new Promise(e=>setTimeout(e,1e3)),t.index.showToast({title:"验证码已发送",icon:"success"}),p()}catch(e){t.index.showToast({title:"发送失败，请重试",icon:"none"})}finally{s.value=!1}}}),p=()=>{r.value=60,c=setInterval(()=>{r.value--,r.value<=0&&(clearInterval(c),c=null)},1e3)},g=()=>e(this,null,function*(){if(v.value){l.value=!0;try{yield new Promise(e=>setTimeout(e,1e3));const e={id:"user_"+Date.now(),nickname:"用户"+u.value.phone.slice(-4),avatar:"/static/images/default-avatar.png",phone:u.value.phone,gender:0,token:"mock_token_"+Date.now()};t.index.setStorageSync("userInfo",e),t.index.setStorageSync("token",e.token),t.index.showToast({title:"登录成功",icon:"success"}),setTimeout(()=>{getCurrentPages().length>1?t.index.navigateBack():t.index.switchTab({url:"/pages/home/<USER>"})},1500)}catch(e){t.index.showToast({title:"登录失败，请重试",icon:"none"})}finally{l.value=!1}}}),x=()=>{o.value="wechat"===o.value?"phone":"wechat"},w=()=>{t.index.setStorageSync("isGuest",!0);getCurrentPages().length>1?t.index.navigateBack():t.index.switchTab({url:"/pages/home/<USER>"})},f=e=>{const n="privacy"===e?"隐私政策":"用户协议";t.index.navigateTo({url:`/pages/common/webview/index?title=${n}&url=https://example.com/${e}`})};return onUnmounted(()=>{c&&clearInterval(c)}),(e,a)=>({a:n._imports_0$2,b:t.o(h),c:i.value,d:t.o(e=>f("privacy")),e:t.o(e=>f("terms")),f:u.value.phone,g:t.o(e=>u.value.phone=e.detail.value),h:u.value.code,i:t.o(e=>u.value.code=e.detail.value),j:t.t(r.value>0?`${r.value}s`:"发送验证码"),k:t.o(m),l:t.p({type:"default",size:"small",disabled:!d.value||r.value>0,loading:s.value}),m:t.o(g),n:t.p({type:"primary",disabled:!v.value,loading:l.value}),o:t.t("wechat"===o.value?"使用手机号登录":"使用微信登录"),p:t.o(x),q:t.o(w)})}}),o=t._export_sfc(a,[["__scopeId","data-v-e19616f2"]]);wx.createPage(o);
