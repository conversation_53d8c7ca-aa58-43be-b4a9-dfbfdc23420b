"use strict";var e=(e,a,t)=>new Promise((l,u)=>{var o=e=>{try{r(t.next(e))}catch(a){u(a)}},n=e=>{try{r(t.throw(e))}catch(a){u(a)}},r=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,n);r((t=t.apply(e,a)).next())});const a=require("../../../common/vendor.js");if(!Array){(a.resolveComponent("mall-navbar")+a.resolveComponent("mall-button"))()}Math||((()=>"../../../components/mall-navbar/mall-navbar.js")+(()=>"../../../components/mall-button/mall-button.js"))();const t=a.defineComponent({__name:"index",setup(t){const l=a.ref(""),u=a.ref({}),o=a.ref(null),n=a.ref([]),r=a.ref("express"),v=a.ref(null),c=a.ref(""),i=a.ref(!1),d=[{value:"express",name:"快递配送",description:"预计3-5天送达",fee:10},{value:"pickup",name:"到店自提",description:"门店地址：xxx",fee:0}],s=a.computed(()=>n.value.reduce((e,a)=>e+a.price*a.quantity,0)),p=a.computed(()=>{const e=d.find(e=>e.value===r.value);return(null==e?void 0:e.fee)||0}),m=a.computed(()=>{var e;return(null==(e=v.value)?void 0:e.value)||0}),f=a.computed(()=>Math.max(0,s.value+p.value-m.value)),h=a.computed(()=>o.value&&n.value.length>0&&!i.value);a.onLoad(e=>{(null==e?void 0:e.productId)&&(l.value=e.productId,e.specs&&(u.value=JSON.parse(e.specs)),x())}),a.onMounted(()=>{g()});const x=()=>e(this,null,function*(){try{n.value=[{id:"1",productId:l.value,productName:"iPhone 15 Pro Max 256GB",productImage:"https://picsum.photos/120/120?random=1",quantity:1,price:9999,totalPrice:9999,selectedSpecs:[{specId:"color",specName:"颜色",optionId:"black",optionName:"深空黑",optionValue:"black"},{specId:"storage",specName:"存储",optionId:"256gb",optionName:"256GB",optionValue:"256gb"}]}]}catch(e){}}),g=()=>e(this,null,function*(){try{o.value={id:"1",userId:"1",name:"张三",phone:"13800138000",province:"广东省",city:"深圳市",district:"南山区",detail:"科技园南区深南大道xxx号",isDefault:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()}}catch(e){}}),y=()=>{a.index.navigateTo({url:"/pages/user/address/index?select=true"})},b=()=>{a.index.navigateTo({url:`/pages/user/coupon/index?select=true&amount=${s.value}`})},I=()=>e(this,null,function*(){var e;if(h.value){i.value=!0;try{n.value,o.value,r.value,null==(e=v.value)||e.id,c.value,s.value,p.value,m.value,f.value;yield new Promise(e=>setTimeout(e,1e3));const t="ORDER_"+Date.now();a.index.redirectTo({url:`/pages/order/pay/index?orderId=${t}&amount=${f.value}`})}catch(t){a.index.showToast({title:"提交失败，请重试",icon:"none"})}finally{i.value=!1}}});return(e,t)=>a.e({a:a.p({title:"确认订单","show-back":!0}),b:o.value},o.value?{c:a.t(o.value.name),d:a.t(o.value.phone),e:a.t(o.value.province),f:a.t(o.value.city),g:a.t(o.value.district),h:a.t(o.value.detail)}:{},{i:a.o(y),j:a.f(n.value,(e,t,l)=>a.e({a:e.productImage,b:a.t(e.productName),c:e.selectedSpecs.length>0},e.selectedSpecs.length>0?{d:a.f(e.selectedSpecs,(e,t,l)=>({a:a.t(e.specName),b:a.t(e.optionName),c:e.specId}))}:{},{e:a.t(e.price),f:a.t(e.quantity),g:e.id})),k:a.f(d,(e,t,l)=>({a:a.t(e.name),b:a.t(e.description),c:a.t(e.fee>0?`¥${e.fee}`:"免费"),d:e.value,e:r.value===e.value?1:"",f:a.o(a=>{return t=e.value,void(r.value=t);var t},e.value)})),l:v.value},v.value?{m:a.t(v.value.name),n:a.t(v.value.value)}:{},{o:a.o(b),p:c.value,q:a.o(e=>c.value=e.detail.value),r:a.t(s.value),s:a.t(p.value>0?`¥${p.value}`:"免费"),t:m.value>0},m.value>0?{v:a.t(m.value)}:{},{w:a.t(f.value),x:a.t(f.value),y:a.o(I),z:a.p({type:"primary",disabled:!h.value,loading:i.value})})}}),l=a._export_sfc(t,[["__scopeId","data-v-e9e3a61e"]]);wx.createPage(l);
