"use strict";var e=(e,t,n)=>new Promise((a,o)=>{var u=e=>{try{r(n.next(e))}catch(t){o(t)}},i=e=>{try{r(n.throw(e))}catch(t){o(t)}},r=e=>e.done?a(e.value):Promise.resolve(e.value).then(u,i);r((n=n.apply(e,t)).next())});const t=require("../../../common/vendor.js");if(!Array){(t.resolveComponent("mall-navbar")+t.resolveComponent("mall-button"))()}Math||((()=>"../../../components/mall-navbar/mall-navbar.js")+(()=>"../../../components/mall-button/mall-button.js"))();const n=t.defineComponent({__name:"index",setup(n){const a=t.ref(!1),o=t.ref(0),u=t.ref([]),i=t.ref("unused"),r=t.ref(""),l=t.ref([{label:"未使用",value:"unused",count:0},{label:"已使用",value:"used",count:0},{label:"已过期",value:"expired",count:0}]);t.onLoad(e=>{"true"===(null==e?void 0:e.select)&&(a.value=!0,e.amount&&(o.value=Number(e.amount)))}),t.onMounted(()=>{s(),c()});const s=()=>e(this,null,function*(){try{const e=[{id:"1",userId:"1",couponId:"coupon_1",coupon:{id:"coupon_1",name:"新用户专享券",type:"amount",value:50,minAmount:200,startTime:new Date(Date.now()-6048e5).toISOString(),endTime:new Date(Date.now()+6048e5).toISOString(),status:"active",description:"新用户专享，满200减50"},status:"unused",createdAt:(new Date).toISOString()},{id:"2",userId:"1",couponId:"coupon_2",coupon:{id:"coupon_2",name:"满减优惠券",type:"amount",value:100,minAmount:500,startTime:new Date(Date.now()-2592e5).toISOString(),endTime:new Date(Date.now()+864e6).toISOString(),status:"active",description:"全场通用，满500减100"},status:"unused",createdAt:(new Date).toISOString()},{id:"3",userId:"1",couponId:"coupon_3",coupon:{id:"coupon_3",name:"限时优惠券",type:"amount",value:30,minAmount:100,startTime:new Date(Date.now()-864e6).toISOString(),endTime:new Date(Date.now()-1728e5).toISOString(),status:"expired",description:"限时活动，满100减30"},status:"expired",createdAt:(new Date).toISOString()},{id:"4",userId:"1",couponId:"coupon_4",coupon:{id:"coupon_4",name:"生日优惠券",type:"amount",value:20,minAmount:50,startTime:new Date(Date.now()-1296e6).toISOString(),endTime:new Date(Date.now()-432e6).toISOString(),status:"active",description:"生日专享，满50减20"},status:"used",usedAt:new Date(Date.now()-864e6).toISOString(),orderId:"order_123",createdAt:(new Date).toISOString()}];u.value=e.filter(e=>e.status===i.value)}catch(e){t.index.showToast({title:"加载失败",icon:"none"})}}),c=()=>e(this,null,function*(){try{const e={unused:2,used:1,expired:1};l.value.forEach(t=>{t.count=e[t.value]||0})}catch(e){}}),d=e=>{if(!a.value)return!0;if("unused"!==e.status)return!1;if(o.value<e.coupon.minAmount)return!1;const t=new Date,n=new Date(e.coupon.startTime),u=new Date(e.coupon.endTime);return t>=n&&t<=u},v=(e,t)=>{const n=new Date(e),a=new Date(t),o=e=>`${e.getFullYear()}.${String(e.getMonth()+1).padStart(2,"0")}.${String(e.getDate()).padStart(2,"0")}`;return`${o(n)} - ${o(a)}`},p=()=>{r.value="",m()},m=()=>{let e=null;r.value&&(e=u.value.find(e=>e.id===r.value)),t.index.$emit("couponSelected",e),t.index.navigateBack()};return(e,n)=>t.e({a:t.p({title:a.value?"选择优惠券":"我的优惠券","show-back":!0}),b:t.f(l.value,(e,n,a)=>t.e({a:t.t(e.label),b:e.count>0},e.count>0?{c:t.t(e.count)}:{},{d:e.value,e:i.value===e.value?1:"",f:t.o(t=>{return n=e.value,void(i.value!==n&&(i.value=n,s()));var n},e.value)})),c:t.f(u.value,(e,n,o)=>t.e({a:t.t(e.coupon.value),b:t.t(e.coupon.minAmount),c:t.t(e.coupon.name),d:e.coupon.description},e.coupon.description?{e:t.t(e.coupon.description)}:{},{f:t.t(v(e.coupon.startTime,e.coupon.endTime)),g:"used"===e.status},("used"===e.status||"expired"===e.status||a.value&&d(e),{}),{h:"expired"===e.status,i:a.value&&!d(e),j:a.value&&d(e)},a.value&&d(e)?{k:t.n(r.value===e.id?"icon-check-circle-fill":"icon-circle")}:{},{l:e.id,m:"expired"===e.status?1:"",n:"used"===e.status?1:"",o:a.value&&r.value===e.id?1:"",p:a.value&&!d(e)?1:"",q:t.o(t=>(e=>{a.value&&d(e)&&(r.value=r.value===e.id?"":e.id)})(e),e.id)})),d:0===u.value.length},(u.value.length,{}),{e:a.value},a.value?{f:t.o(p),g:t.o(m),h:t.p({type:"primary",disabled:!r.value})}:{})}}),a=t._export_sfc(n,[["__scopeId","data-v-689f0ada"]]);wx.createPage(a);
